/**
 * 矩阵主组件
 * 🎯 核心价值：纯渲染组件，数据驱动视图，零业务逻辑
 * 📦 功能范围：矩阵渲染、交互事件传递、性能优化
 * 🔄 架构设计：完全无状态组件，所有逻辑通过props注入
 */

'use client';

import { matrixCore } from '@/core/matrix/MatrixCore';
import { useMatrixConfig, useMatrixData, useMatrixStore } from '@/core/matrix/MatrixStore';
import type {
  BusinessMode,
  Coordinate,
  MatrixConfig,
} from '@/core/matrix/MatrixTypes';
import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { createInteractionEvent } from '@/core/matrix/MatrixCore';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;

  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 交互事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellHover?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellFocus?: (coordinate: Coordinate, event: React.FocusEvent) => void;
  onModeChange?: (mode: BusinessMode) => void;
}

// ===== 主组件 =====

const MatrixComponent: React.FC<MatrixProps> = ({
  configOverride,
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onCellHover,
  onCellFocus,
  onModeChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);
  const [isClient, setIsClient] = useState(false);

  // 获取状态
  const matrixData = useMatrixData();
  const matrixConfig = useMatrixConfig();
  const {
    initializeMatrix,
    selectCell,
    hoverCell,
    focusCell,
    getCellRenderData,
  } = useMatrixStore();

  // 合并配置
  const finalConfig = { ...matrixConfig, ...configOverride };

  // 动态计算矩阵容器尺寸 - 颜色模式下为1级格子放大预留空间
  const isColorMode = finalConfig.mainMode === 'color';



  // 确保客户端渲染一致性
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化
  useEffect(() => {
    if (!isInitialized.current && containerRef.current && isClient) {
      // 初始化矩阵数据
      if (matrixData.cells.size === 0) {
        initializeMatrix();
      }
      isInitialized.current = true;
    }
  }, [initializeMatrix, matrixData.cells.size, isClient]);

  // 渲染矩阵单元格
  const renderMatrixCells = () => {
    const cells = [];
    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        const cellRenderData = getCellRenderData(x, y);
        const key = `${x},${y}`;

        // 获取单元格数据以检测1级坐标
        const cell = matrixData.cells.get(key);
        const isLevel1 = cell?.level === 1;

        // 颜色模式下1级格子放大
        const cellSize = (isColorMode && isLevel1) ? 39 : 33;
        // 计算统一居中偏移：所有1级格子都居中，容器扩展保证不裁切
        const centerOffset = (isColorMode && isLevel1) ? -3 : 0; // (39-33)/2 = 3px偏移
        // 1级格子样式增强
        const isEnhanced = isColorMode && isLevel1;
        const zIndex = isEnhanced ? 10 : 1; // 1级格子在最上层
        const borderRadius = isEnhanced ? '50%' : '4px'; // 1级格子圆形，普通格子圆角



        cells.push(
          <div
            key={key}
            data-x={x}
            data-y={y}
            className={cellRenderData?.className || 'matrix-cell'}
            style={{
              position: 'absolute',
              left: `${x * 34 + centerOffset}px`, // 应用统一居中偏移
              top: `${y * 34 + centerOffset}px`,
              width: `${cellSize}px`,
              height: `${cellSize}px`,
              backgroundColor: cellRenderData?.style?.backgroundColor || '#ffffff',
              color: cellRenderData?.style?.color || '#000000',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              userSelect: 'none',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              transition: 'width 0.2s ease, height 0.2s ease, border 0.2s ease, border-radius 0.2s ease, z-index 0.2s ease', // 平滑过渡
              ...cellRenderData?.style, // 动态样式（包括fontSize）先应用
              // 1级格子的特殊样式最后应用，确保不被覆盖
              borderRadius, // 动态圆角：1级格子圆形，普通格子圆角
              zIndex, // 1级格子在最上层
            }}
          >
            {cellRenderData?.content || ''}
          </div>
        );
      }
    }
    return cells;
  };

  // 处理单元格点击
  const handleCellClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };

      // 更新状态
      selectCell(x, y, event.ctrlKey || event.metaKey);

      // 创建交互事件
      const interactionEvent = createInteractionEvent('click', coordinate, {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
      });

      // 处理业务逻辑
      const cell = matrixData.cells.get(`${x},${y}`);
      if (cell) {
        matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
      }

      // 调用外部回调
      onCellClick?.(coordinate, event);
    }
  }, [matrixData.cells, finalConfig, selectCell, onCellClick]);

  // 处理双击
  const handleCellDoubleClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      onCellDoubleClick?.(coordinate, event);
    }
  }, [onCellDoubleClick]);

  // 处理悬停
  const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      hoverCell(x, y);
      onCellHover?.(coordinate, event);
    }
  }, [hoverCell, onCellHover]);

  // 处理焦点
  const handleCellFocus = useCallback((event: React.FocusEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      focusCell(x, y);
      onCellFocus?.(coordinate, event);
    }
  }, [focusCell, onCellFocus]);



  // 使用统一的基础尺寸配置，与 useResponsiveControls 保持一致
  const MATRIX_BASE_SIZE = 1134; // 与 useResponsiveControls 中的 MATRIX_BASE_SIZE 保持一致
  // 颜色模式下需要更多空间：1级格子39px + 负偏移3px，四边各扩展6px确保不裁切
  const containerExpansion = isColorMode ? 12 : 0; // 四边扩展：6px * 2 = 12px，为1级格子留足空间
  const MATRIX_ACTUAL_SIZE = MATRIX_BASE_SIZE + containerExpansion;



  // 视口样式（外层滚动容器）- 响应式设计，保持1:1比例
  const viewportStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    maxWidth: `${MATRIX_ACTUAL_SIZE}px`,
    maxHeight: `${MATRIX_ACTUAL_SIZE}px`,
    aspectRatio: '1 / 1', // 保持1:1比例
    overflow: 'auto',
    // 移除边框以避免占用内部空间，确保矩阵完全显示
    borderRadius: '6px', // 与格子统一的圆角
    transition: 'max-width 0.2s ease, max-height 0.2s ease', // 平滑过渡
    ...style,
    // 颜色模式下强制使用正确的尺寸，覆盖外部传入的响应式样式
    ...(isColorMode && {
      maxWidth: `${MATRIX_ACTUAL_SIZE}px`,
      maxHeight: `${MATRIX_ACTUAL_SIZE}px`,
    }),
  };

  // 容器样式（内层矩阵容器）- 动态尺寸，支持1级格子放大
  const containerPadding = isColorMode ? 6 : 0; // 恢复内边距，为1级格子负偏移留出空间
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: `${MATRIX_ACTUAL_SIZE}px`,
    height: `${MATRIX_ACTUAL_SIZE}px`,
    padding: `${containerPadding}px`,
    boxSizing: 'border-box', // 确保padding包含在总尺寸内
    userSelect: 'none',
    minWidth: `${MATRIX_ACTUAL_SIZE}px`,
    minHeight: `${MATRIX_ACTUAL_SIZE}px`,
    transition: 'width 0.2s ease, height 0.2s ease, padding 0.2s ease', // 平滑尺寸变化
  };



  // 在客户端渲染完成前显示占位符
  if (!isClient) {
    return (
      <div
        className={`matrix-viewport ${className}`}
        style={{ ...viewportStyle, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <div className="text-gray-500">矩阵加载中...</div>
      </div>
    );
  }

  return (
    <div
      className={`matrix-viewport ${className}`}
      style={viewportStyle}
    >
      <div
        ref={containerRef}
        className="matrix-container"
        style={containerStyle}
        onClick={handleCellClick}
        onDoubleClick={handleCellDoubleClick}
        onMouseEnter={handleCellMouseEnter}
        onFocus={handleCellFocus}
        tabIndex={0}
        role="grid"
        aria-label="矩阵网格"
        aria-rowcount={33}
        aria-colcount={33}
      >
        {renderMatrixCells()}
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const Matrix = memo(MatrixComponent);

Matrix.displayName = 'Matrix';

export default Matrix;
